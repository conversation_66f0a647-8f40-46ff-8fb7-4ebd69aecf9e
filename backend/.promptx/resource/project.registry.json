{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-10T09:23:08.637Z", "updatedAt": "2025-08-10T09:23:08.639Z", "resourceCount": 4}, "resources": [{"id": "golang-development-process", "source": "project", "protocol": "execution", "name": "Golang Development Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/golang-backend-developer/execution/golang-development-process.execution.md", "metadata": {"createdAt": "2025-08-10T09:23:08.638Z", "updatedAt": "2025-08-10T09:23:08.638Z", "scannedAt": "2025-08-10T09:23:08.638Z", "path": "domain/golang-backend-developer/execution/golang-development-process.execution.md"}}, {"id": "golang-backend-developer", "source": "project", "protocol": "role", "name": "Golang Backend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/golang-backend-developer/golang-backend-developer.role.md", "metadata": {"createdAt": "2025-08-10T09:23:08.638Z", "updatedAt": "2025-08-10T09:23:08.638Z", "scannedAt": "2025-08-10T09:23:08.638Z", "path": "domain/golang-backend-developer/golang-backend-developer.role.md"}}, {"id": "golang-backend-expertise", "source": "project", "protocol": "knowledge", "name": "Golang Backend Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/golang-backend-developer/knowledge/golang-backend-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-10T09:23:08.638Z", "updatedAt": "2025-08-10T09:23:08.638Z", "scannedAt": "2025-08-10T09:23:08.638Z", "path": "domain/golang-backend-developer/knowledge/golang-backend-expertise.knowledge.md"}}, {"id": "golang-backend-thinking", "source": "project", "protocol": "thought", "name": "Golang Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/golang-backend-developer/thought/golang-backend-thinking.thought.md", "metadata": {"createdAt": "2025-08-10T09:23:08.638Z", "updatedAt": "2025-08-10T09:23:08.638Z", "scannedAt": "2025-08-10T09:23:08.638Z", "path": "domain/golang-backend-developer/thought/golang-backend-thinking.thought.md"}}], "stats": {"totalResources": 4, "byProtocol": {"execution": 1, "role": 1, "knowledge": 1, "thought": 1}, "bySource": {"project": 4}}}